import{d as o,u as n,e as b,az as m,j as e,Q as S,aA as h,R as x,S as j,T as _,h as p,U as g,aB as u,W as v,o as y,r as f}from"./index-HMJiLbu5.js";const k=()=>{const i=o(),s=n(),r=b(m),a=l=>{switch(i(u(l)),l){case"dashboard":s("/seller/dashboard");break;case"my-sports-strategies":s("/seller/my-sports-strategies");break;case"requests":s("/seller/requests");break;case"bids":s("/seller/bids");break;case"cards":s("/seller/cards");break;case"profile":s("/seller/profile");break;default:s("/seller/dashboard")}},t=()=>{i(v()),s("/")};return e.jsx("div",{className:"SellerSidebar",children:e.jsx("div",{className:"SellerSidebar__container",children:e.jsxs("ul",{className:"SellerSidebar__menu",children:[e.jsxs("li",{className:`SellerSidebar__item ${r==="dashboard"?"active":""}`,onClick:()=>a("dashboard"),children:[e.jsx(S,{className:"SellerSidebar__icon"}),e.jsx("span",{children:"Dashboard"})]}),e.jsxs("li",{className:`SellerSidebar__item ${r==="my-sports-strategies"?"active":""}`,onClick:()=>a("my-sports-strategies"),children:[e.jsx(h,{className:"SellerSidebar__icon"}),e.jsx("span",{children:"My Sports Strategies"})]}),e.jsxs("li",{className:`SellerSidebar__item ${r==="requests"?"active":""}`,onClick:()=>a("requests"),children:[e.jsx(x,{className:"SellerSidebar__icon"}),e.jsx("span",{children:"Requests"})]}),e.jsxs("li",{className:`SellerSidebar__item ${r==="bids"?"active":""}`,onClick:()=>a("bids"),children:[e.jsx(j,{className:"SellerSidebar__icon"}),e.jsx("span",{children:"Bids"})]}),e.jsxs("li",{className:`SellerSidebar__item ${r==="cards"?"active":""}`,onClick:()=>a("cards"),children:[e.jsx(_,{className:"SellerSidebar__icon"}),e.jsx("span",{children:"My Cards"})]}),e.jsxs("li",{className:`SellerSidebar__item ${r==="profile"?"active":""}`,onClick:()=>a("profile"),children:[e.jsx(p,{className:"SellerSidebar__icon"}),e.jsx("span",{children:"My Profile"})]}),e.jsxs("li",{className:"SellerSidebar__item SellerSidebar__logout",onClick:t,children:[e.jsx(g,{className:"SellerSidebar__icon"}),e.jsx("span",{children:"Logout"})]})]})})})},q=({children:i})=>{const s=o(),r=y(),a=b(m),t={"/seller/dashboard":"dashboard","/seller/my-sports-strategies":"my-sports-strategies","/seller/my-sports-strategies/add":"my-sports-strategies","/seller/requests":"requests","/seller/bids":"bids","/seller/cards":"cards","/seller/profile":"profile"},l={dashboard:{title:"Dashboard",icon:e.jsx(S,{})},"my-sports-strategies":{title:"My Sports Strategies",icon:e.jsx(h,{})},requests:{title:"Requests",icon:e.jsx(x,{})},bids:{title:"Bids",icon:e.jsx(j,{})},cards:{title:"My Cards",icon:e.jsx(_,{})},profile:{title:"My Profile",icon:e.jsx(p,{})}},d=l[a]||l.dashboard;f.useEffect(()=>{const c=t[r.pathname];c&&c!==a&&s(u(c))},[r.pathname,a,s]);const N=n();return e.jsx("div",{className:"SellerLayout",children:e.jsxs("div",{className:"container max-container",children:[e.jsx("div",{className:"sidebar",children:e.jsx(k,{})}),e.jsxs("div",{className:"outerdiv",children:[e.jsxs("div",{className:"bordrdiv mb-30",children:[e.jsxs("h2",{className:"SellerLayout__title",children:[d.icon,d.title]}),a==="my-sports-strategies"&&e.jsx("button",{className:"add-strategy-btn btn btn-outline ",onClick:()=>N("/seller/my-sports-strategies/add"),children:"Add New Strategy"})]}),e.jsx("div",{className:"content",children:e.jsx("div",{className:"SellerLayout__content",children:i})})]})]})})};export{q as S};
