import React, { useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import { useLocation } from "react-router-dom";
import {
  selectActiveTab,
  setActiveTab,
} from "../../redux/slices/sellerDashboardSlice";
import SellerSidebar from "./SellerSidebar";
import "../../styles/SellerLayout.css";

// Icons
import { MdDashboard } from "react-icons/md";
import { FaUser } from "react-icons/fa";
import { MdRequestPage } from "react-icons/md";
import { FaGavel } from "react-icons/fa";
import { MdVideoLibrary } from "react-icons/md";
import { FaCreditCard } from "react-icons/fa";
import { useNavigate } from "react-router-dom";


const SellerLayout = ({ children }) => {
  const dispatch = useDispatch();
  const location = useLocation();
  const activeTab = useSelector(selectActiveTab);

  // Map routes to tabs
  const routeToTabMap = {
    "/seller/dashboard": "dashboard",
    "/seller/my-sports-strategies": "my-sports-strategies",
    "/seller/my-sports-strategies/add": "my-sports-strategies",
    "/seller/requests": "requests",
    "/seller/bids": "bids",
    "/seller/cards": "cards",
    "/seller/profile": "profile",
  };

  // Check if current page is Add New Strategy page
  const isAddStrategyPage = location.pathname === "/seller/my-sports-strategies/add";

  // Header configuration for each page
  const headerConfig = {
    "dashboard": {
      title: "Dashboard",

      icon: <MdDashboard />
    },
    "my-sports-strategies": {
      title: "My Sports Strategies",

      icon: <MdVideoLibrary />
    },
    "requests": {
      title: "Requests",

      icon: <MdRequestPage />
    },
    "bids": {
      title: "Bids",

      icon: <FaGavel />
    },
    "cards": {
      title: "My Cards",

      icon: <FaCreditCard />
    },
    "profile": {
      title: "My Profile",

      icon: <FaUser />
    }
  };

  // Get current header info
  const currentHeader = headerConfig[activeTab] || headerConfig["dashboard"];

  // Update active tab based on current route
  useEffect(() => {
    const currentTab = routeToTabMap[location.pathname];
    if (currentTab && currentTab !== activeTab) {
      dispatch(setActiveTab(currentTab));
    }
  }, [location.pathname, activeTab, dispatch]);
  const navigate = useNavigate();
  return (
    <div className="SellerLayout">
      <div className="container max-container">
        <div className="sidebar">
          <SellerSidebar />
        </div>

     <div className="outerdiv">
     {!isAddStrategyPage && (
       <div className="bordrdiv mb-30">
            <h2 className="SellerLayout__title">
              {currentHeader.icon}
              {currentHeader.title}
            </h2>
            {activeTab === "my-sports-strategies" && (
    <button className="add-strategy-btn btn btn-outline " onClick={() => navigate("/seller/my-sports-strategies/add")}>
      Add New Strategy
    </button>
  )}
          </div>
     )}
          <div className="content">
          <div className="SellerLayout__content">
            {children}
          </div>
        </div>
     </div>
      </div>
    </div>
  );
};

export default SellerLayout;
