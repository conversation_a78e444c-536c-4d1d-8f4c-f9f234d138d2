import{e as d,ao as c,j as s,an as l}from"./index-HMJiLbu5.js";import{S as n}from"./SellerLayout-BF_wrScs.js";import{a as i}from"./index-zvlxN9p5.js";const x=()=>{const t=d(c);return s.jsx(n,{children:s.jsx("div",{className:"seller-requests-container",children:s.jsxs("table",{className:"requests-table",children:[s.jsx("thead",{children:s.jsxs("tr",{children:[s.jsx("th",{children:"No."}),s.jsx("th",{children:"Request Id"}),s.jsx("th",{children:"Videos/Documents"}),s.jsx("th",{children:"Date"}),s.jsx("th",{children:"Price"}),s.jsx("th",{children:"Requested Amount"}),s.jsx("th",{children:"Requested Customer"}),s.jsx("th",{children:"Action"})]})}),s.jsx("tbody",{children:t.map((e,r)=>s.jsxs("tr",{children:[s.jsx("td",{children:r+1}),s.jsx("td",{children:e.id}),s.jsx("td",{children:s.jsxs("div",{className:"video-doc",children:[s.jsx("img",{src:e.image,alt:e.title}),s.jsx("span",{children:e.title})]})}),s.jsxs("td",{children:[e.date," | 4:50PM"]}),s.jsx("td",{children:e.price}),s.jsx("td",{children:e.requestedAmount}),s.jsx("td",{children:e.requestedCustomer}),s.jsx("td",{children:s.jsxs("div",{className:"action-icons",children:[s.jsx(l,{className:"action-icon"}),s.jsx(i,{className:"action-icon"})]})})]},e.id))})]})})})};export{x as default};
