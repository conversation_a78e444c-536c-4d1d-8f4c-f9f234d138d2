import{aj as l,u as d,e as r,ak as o,K as n,j as e}from"./index-HMJiLbu5.js";import{S as c}from"./StrategyCard-C4Heew73.js";const y=()=>{const{id:a}=l();d();const t=r(i=>o(i,a)),s=r(n).filter(i=>String(i.id)!==String(a));return t?e.jsxs("div",{className:"item-details details-page",style:{display:"flex",flexDirection:"column",gap:"2rem",padding:"2rem 0"},children:[e.jsxs("div",{className:"max-container",style:{display:"flex",flexWrap:"wrap",gap:"2rem"},children:[e.jsxs("div",{className:"main-content",style:{flex:2,minWidth:0},children:[e.jsx("h2",{style:{color:"var(--secondary-color)",marginBottom:"1rem"},children:t.title}),e.jsx("img",{src:t.image,alt:t.title,style:{width:"100%",maxWidth:500,borderRadius:"var(--border-radius-large)",marginBottom:"1rem"}}),e.jsx("div",{className:"video-placeholder",style:{background:"var(--light-gray)",height:250,borderRadius:12,display:"flex",alignItems:"center",justifyContent:"center",marginBottom:24},children:e.jsx("span",{style:{color:"var(--dark-gray)"},children:"[Video Player Placeholder]"})}),e.jsxs("div",{className:"tabs-placeholder",style:{display:"flex",gap:24,marginBottom:24},children:[e.jsxs("div",{style:{flex:1,background:"var(--white)",borderRadius:8,padding:16,border:"1px solid var(--light-gray)"},children:[e.jsx("strong",{children:"Description"}),e.jsx("p",{style:{marginTop:8,color:"var(--dark-gray)"},children:"This is a placeholder for the strategy description. Add more details about the strategy here."})]}),e.jsxs("div",{style:{flex:1,background:"var(--white)",borderRadius:8,padding:16,border:"1px solid var(--light-gray)"},children:[e.jsx("strong",{children:"The Coach"}),e.jsx("p",{style:{marginTop:8,color:"var(--dark-gray)"},children:t.coach})]})]}),e.jsx("button",{className:`action-button ${t.type==="bid"?"bid-button":"buy-button"}`,style:{minWidth:120,fontSize:18},children:t.type==="bid"?"Bid Now":"Buy Now"})]}),e.jsx("aside",{className:"sidebar",style:{flex:1,minWidth:260,background:"var(--white)",borderRadius:12,padding:24,border:"1px solid var(--light-gray)",height:"fit-content"},children:e.jsxs("div",{className:"price-info",style:{marginBottom:16},children:[e.jsxs("div",{style:{fontWeight:700,fontSize:22,color:"var(--secondary-color)"},children:["$",t.price.toFixed(2)]}),e.jsx("div",{style:{color:"var(--dark-gray)",marginTop:8},children:"Dummy info: This is a sidebar for price and other details."})]})})]}),e.jsxs("section",{className:"related-strategies max-container",style:{width:"100%"},children:[e.jsx("h3",{style:{color:"var(--secondary-color)",marginBottom:16},children:"Related Strategies"}),e.jsx("div",{className:"strategy-grid",style:{display:"grid",gridTemplateColumns:"repeat(auto-fit, minmax(260px, 1fr))",gap:24},children:s.slice(0,4).map(i=>e.jsx(c,{id:i.id,image:i.image,title:i.title,coach:i.coach,price:i.price,hasVideo:i.hasVideo,type:i.type||"buy"},i.id))})]})]}):e.jsx("div",{className:"item-details",children:"Not found"})};export{y as default};
