import{r,j as e}from"./index-HMJiLbu5.js";const h=({onNext:o})=>{const[n,c]=r.useState([{school:"",position:"",from:"",to:""}]),l=(a,i,s)=>{const d=n.map((x,p)=>p===a?{...x,[i]:s}:x);c(d)},t=()=>{c([...n,{school:"",position:"",from:"",to:""}])};return e.jsxs("div",{className:"seller-onboarding-step1-container max-container",children:[e.jsxs("div",{className:"progress-bar",children:[e.jsx("div",{className:"step active",children:"1"}),e.jsx("div",{className:"progress-line"}),e.jsx("div",{className:"step",children:"2"})]}),e.jsxs("div",{className:"form-grid",children:[e.jsxs("div",{className:"description-section",children:[e.jsx("div",{className:"section-title",children:"Description"}),e.jsx("div",{className:"description-box",children:e.jsx("textarea",{className:"description-textarea",placeholder:"Write Description..",rows:3})})]}),e.jsxs("div",{className:"profile-experience-grid",children:[e.jsxs("div",{className:"profile-pic-section",children:[e.jsx("div",{className:"section-title",children:"Profile Pic"}),e.jsxs("div",{className:"avatar-upload",children:[e.jsx("div",{className:"avatar-placeholder",children:e.jsxs("svg",{width:"64",height:"64",viewBox:"0 0 64 64",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("circle",{cx:"32",cy:"32",r:"32",fill:"var(--light-gray)"}),e.jsx("ellipse",{cx:"32",cy:"27",rx:"12",ry:"12",fill:"#fff"}),e.jsx("ellipse",{cx:"32",cy:"50",rx:"16",ry:"10",fill:"#fff"})]})}),e.jsx("button",{className:"btn btn-outline upload-btn",children:"Upload Photo"})]})]}),e.jsxs("div",{className:"experience-section",children:[e.jsx("div",{className:"section-title",children:"Experience"}),n.map((a,i)=>e.jsxs("div",{className:"experience-row",children:[e.jsx("input",{type:"text",className:"input",placeholder:"Enter School Name",value:a.school,onChange:s=>l(i,"school",s.target.value)}),e.jsx("input",{type:"text",className:"input",placeholder:"Enter Position",value:a.position,onChange:s=>l(i,"position",s.target.value)}),e.jsxs("div",{className:"year-fields",children:[e.jsx("input",{type:"text",className:"input year-input",placeholder:"From Year",value:a.from,onChange:s=>l(i,"from",s.target.value)}),e.jsx("input",{type:"text",className:"input year-input",placeholder:"To Year",value:a.to,onChange:s=>l(i,"to",s.target.value)})]})]},i)),e.jsx("div",{className:"add-more-link",onClick:t,children:"+ Add More"})]})]})]}),e.jsx("div",{className:"next-btn-row",children:e.jsx("button",{className:"btn btn-primary next-btn",onClick:o,children:"Next"})})]})},j=()=>{const[o,n]=r.useState(1),[c,l]=r.useState(""),[t,a]=r.useState({facebook:"",linkedin:"",twitter:""}),i=(s,d)=>{a({...t,[s]:d})};return e.jsx("div",{className:"seller-onboarding-wrapper max-container",children:o===1?e.jsx(h,{onNext:()=>n(2)}):e.jsxs("div",{className:"seller-onboarding-step2-container",children:[e.jsxs("div",{className:"progress-bar",children:[e.jsx("div",{className:"step complete",children:"1"}),e.jsx("div",{className:"progress-line"}),e.jsx("div",{className:"step active",children:"2"})]}),e.jsxs("div",{className:"section-block",children:[e.jsx("div",{className:"section-title",children:"Minimum Customer Training Cost"}),e.jsx("input",{type:"number",className:"input min-cost-input",placeholder:"Enter amount",value:c,onChange:s=>l(s.target.value)})]}),e.jsxs("div",{className:"section-block",children:[e.jsx("div",{className:"section-title",children:"Social Media Account"}),e.jsxs("div",{className:"social-inputs-grid",children:[e.jsxs("div",{className:"social-input-row",children:[e.jsx("span",{className:"social-icon facebook",children:e.jsxs("svg",{width:"20",height:"20",fill:"none",viewBox:"0 0 20 20",children:[e.jsx("path",{d:"M18 10A8 8 0 1 0 10 18V12.89H8.1V10.89H10V9.22C10 7.5 11.17 6.5 12.72 6.5C13.44 6.5 14.2 6.62 14.2 6.62V8.6H13.23C12.27 8.6 12 9.18 12 9.77V10.89H14.1L13.8 12.89H12V18A8 8 0 0 0 18 10Z",fill:"#1877F3"}),e.jsx("path",{d:"M13.8 12.89L14.1 10.89H12V9.77C12 9.18 12.27 8.6 13.23 8.6H14.2V6.62S13.44 6.5 12.72 6.5C11.17 6.5 10 7.5 10 9.22V10.89H8.1V12.89H10V18C10.67 18 11.32 17.93 11.95 17.8V12.89H13.8Z",fill:"#fff"})]})}),e.jsx("input",{type:"text",className:"input social-input",placeholder:"Facebook URL",value:t.facebook,onChange:s=>i("facebook",s.target.value)})]}),e.jsxs("div",{className:"social-input-row",children:[e.jsx("span",{className:"social-icon linkedin",children:e.jsxs("svg",{width:"20",height:"20",fill:"none",viewBox:"0 0 20 20",children:[e.jsx("circle",{cx:"10",cy:"10",r:"10",fill:"#0A66C2"}),e.jsx("path",{d:"M6.94 8.5H4.98V15H6.94V8.5ZM5.96 7.5C6.6 7.5 7.1 7 7.1 6.36C7.1 5.72 6.6 5.22 5.96 5.22C5.32 5.22 4.82 5.72 4.82 6.36C4.82 7 5.32 7.5 5.96 7.5ZM15 15H13.04V11.5C13.04 10.67 12.37 10 11.54 10C10.71 10 10.04 10.67 10.04 11.5V15H8.08V8.5H10.04V9.38C10.41 8.81 11.13 8.5 11.54 8.5C13.01 8.5 15 9.44 15 11.5V15Z",fill:"#fff"})]})}),e.jsx("input",{type:"text",className:"input social-input",placeholder:"LinkedIn URL",value:t.linkedin,onChange:s=>i("linkedin",s.target.value)})]}),e.jsxs("div",{className:"social-input-row",children:[e.jsx("span",{className:"social-icon twitter",children:e.jsxs("svg",{width:"20",height:"20",fill:"none",viewBox:"0 0 20 20",children:[e.jsx("circle",{cx:"10",cy:"10",r:"10",fill:"#1DA1F2"}),e.jsx("path",{d:"M15.32 8.13C15.33 8.23 15.33 8.33 15.33 8.43C15.33 11.13 13.29 14.13 9.5 14.13C8.37 14.13 7.31 13.8 6.4 13.23C6.56 13.25 6.72 13.26 6.89 13.26C7.82 13.26 8.66 12.95 9.36 12.44C8.48 12.43 7.74 11.87 7.49 11.07C7.62 11.09 7.75 11.11 7.89 11.11C8.08 11.11 8.27 11.08 8.45 11.03C7.54 10.85 6.85 10.03 6.85 9.06V9.04C7.11 9.19 7.42 9.28 7.75 9.29C7.19 8.91 6.81 8.28 6.81 7.57C6.81 7.23 6.9 6.92 7.07 6.66C8.04 7.84 9.47 8.62 11.07 8.7C11.04 8.56 11.03 8.41 11.03 8.27C11.03 7.29 11.82 6.5 12.8 6.5C13.29 6.5 13.73 6.7 14.03 7.04C14.4 6.97 14.75 6.85 15.06 6.68C14.95 7.06 14.7 7.37 14.37 7.56C14.7 7.53 15.01 7.44 15.32 7.3C15.06 7.62 14.72 7.89 14.34 8.13H15.32Z",fill:"#fff"})]})}),e.jsx("input",{type:"text",className:"input social-input",placeholder:"Twitter URL",value:t.twitter,onChange:s=>i("twitter",s.target.value)})]})]})]}),e.jsx("div",{className:"next-btn-row",children:e.jsx("button",{className:"btn btn-primary next-btn",children:"Next"})})]})})};export{j as default};
