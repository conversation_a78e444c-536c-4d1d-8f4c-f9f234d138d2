import{j as s,al as n,L as l}from"./index-HMJiLbu5.js";const m=({image:t,title:a,coach:r,price:c,hasVideo:d,id:i,type:e="buy"})=>s.jsxs("div",{className:"strategy-card-component strategy-card",children:[s.jsxs("div",{className:"strategy-card-image",children:[s.jsx("img",{src:t,alt:a}),d&&s.jsx("div",{className:"video-icon",children:s.jsx(n,{})})]}),s.jsxs("div",{className:"strategy-card-content",children:[s.jsx("h3",{className:"strategy-card-title",children:a}),s.jsxs("p",{className:"strategy-card-coach",children:["By ",r]}),s.jsxs("div",{className:"strategy-card-footer",children:[s.jsxs("span",{className:"strategy-card-price",children:["$",c.toFixed(2)]}),s.jsx(l,{to:`/buyer/details/${i}`,className:`action-button ${e==="bid"?"bid-button":"buy-button"}`,children:e==="bid"?"Bid Now":"Buy Now"})]})]})]});export{m as S};
