import{d as w,e as F,r,X as P,f as j,j as e,h as p,i as D,Y as A,Z as R,$ as k,a0 as L}from"./index-HMJiLbu5.js";import{S as q}from"./SellerLayout-BF_wrScs.js";import{t as _}from"./toast-DtL9P6Ll.js";const X=()=>{const s=w(),{user:l,isLoading:c,isSuccess:N,isError:h,error:f}=F(t=>t.auth),[a,u]=r.useState({firstName:"",lastName:"",email:"",mobile:"",profileImage:""}),[S,v]=r.useState(null),[g,b]=r.useState(null);r.useEffect(()=>{s(P())},[s]),r.useEffect(()=>{l&&l.data?u({firstName:l.data.firstName||"",lastName:l.data.lastName||"",email:l.data.email||"",mobile:l.data.mobile||"",profileImage:l.data.profileImage||""}):l&&u({firstName:l.firstName||"",lastName:l.lastName||"",email:l.email||"",mobile:l.mobile||"",profileImage:l.profileImage||""})},[l]);const[n,d]=r.useState(!1),[I,x]=r.useState(!1);r.useEffect(()=>{n&&N&&!c&&(_.success("Profile updated successfully!"),s(j()),d(!1),s(P())),n&&h&&f&&(_.error(f.message||"Failed to update profile"),s(j()),d(!1))},[N,h,f,c,s,n]);const m=t=>{const{name:i,value:o}=t.target;u({...a,[i]:o})},y=async t=>{t.preventDefault(),d(!0);try{let i=a.profileImage;S&&(i=(await s(k(S)).unwrap()).data.fileUrl);const o={firstName:a.firstName,lastName:a.lastName,profileImage:i};s(L(o))}catch{_.error("Failed to upload image or update profile"),d(!1)}},E=t=>{const i=t.target.files[0];if(i){v(i),x(!1);const o=new FileReader;o.onloadend=()=>{b(o.result)},o.readAsDataURL(i)}},C=()=>{x(!0)},U=()=>{window.confirm("Are you sure you want to delete your account? This action cannot be undone.")};return e.jsx(q,{children:e.jsxs("div",{className:"SellerProfile",children:[e.jsxs("div",{className:"SellerProfile__container",children:[e.jsxs("div",{className:"SellerProfile__left-section",children:[e.jsxs("div",{className:"SellerProfile__form-row",children:[e.jsx("div",{className:"SellerProfile__input-field",children:e.jsxs("div",{className:"SellerProfile__input-container",children:[e.jsx("div",{className:"SellerProfile__input-icon",children:e.jsx(p,{})}),e.jsx("input",{type:"text",id:"firstName",name:"firstName",value:a.firstName,onChange:m,placeholder:"First Name",required:!0,className:"SellerProfile__input"})]})}),e.jsx("div",{className:"SellerProfile__input-field",children:e.jsxs("div",{className:"SellerProfile__input-container",children:[e.jsx("div",{className:"SellerProfile__input-icon",children:e.jsx(p,{})}),e.jsx("input",{type:"text",id:"lastName",name:"lastName",value:a.lastName,onChange:m,placeholder:"Last Name",required:!0,className:"SellerProfile__input"})]})})]}),e.jsxs("div",{className:"SellerProfile__form-row",children:[e.jsx("div",{className:"SellerProfile__input-field",children:e.jsxs("div",{className:"SellerProfile__input-container",children:[e.jsx("div",{className:"SellerProfile__input-icon",children:e.jsx(D,{})}),e.jsx("input",{type:"email",id:"email",name:"email",value:a.email,onChange:m,placeholder:"Email Address",required:!0,disabled:!0,className:"SellerProfile__input SellerProfile__input--disabled"})]})}),e.jsx("div",{className:"SellerProfile__input-field",children:e.jsxs("div",{className:"SellerProfile__input-container",children:[e.jsx("div",{className:"SellerProfile__input-icon",children:e.jsx(A,{})}),e.jsx("input",{type:"tel",id:"mobile",name:"mobile",value:a.mobile,onChange:m,placeholder:"Mobile Number",disabled:!0,className:"SellerProfile__input SellerProfile__input--disabled"})]})})]})]}),e.jsx("div",{className:"SellerProfile__right-section",children:e.jsxs("div",{className:"SellerProfile__image-container",children:[e.jsx("h3",{className:"SellerProfile__image-title",children:"Profile Image"}),e.jsx("div",{className:"SellerProfile__image",children:g||a.profileImage&&!I?e.jsx("img",{src:g||R(a.profileImage),alt:"Profile",onError:C}):e.jsx("div",{className:"SellerProfile__placeholder",children:a.firstName&&a.lastName?`${a.firstName.charAt(0)}${a.lastName.charAt(0)}`:e.jsx(p,{className:"SellerProfile__user-icon"})})}),e.jsx("button",{className:"SellerProfile__upload-btn",onClick:()=>document.getElementById("profile-image-upload").click(),children:"Upload Photo"}),e.jsx("input",{type:"file",id:"profile-image-upload",accept:"image/*",onChange:E,style:{display:"none"}})]})})]}),e.jsxs("div",{className:"SellerProfile__buttons mt-30",children:[e.jsx("button",{type:"button",className:"SellerProfile__delete-btn",onClick:U,children:"Delete Account"}),e.jsx("button",{type:"button",className:"SellerProfile__save-btn",onClick:y,disabled:n||c,children:n||c?"Updating...":"Update & Save"})]})]})})};export{X as default};
