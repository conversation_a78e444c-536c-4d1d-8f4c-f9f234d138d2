const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/index-HMJiLbu5.js","assets/index-Be7kwsvq.css"])))=>i.map(i=>d[i]);
import{r as p,j as e,F as $,a as D,b as y,c as M,u as O,d as U,e as L,L as A,f as b,l as B,g as q,_ as z}from"./index-HMJiLbu5.js";import{F as V,G as Y,f as S}from"./firebaseService-TvL0nZaZ.js";import{t as d}from"./toast-DtL9P6Ll.js";const I=({isOpen:n,onClose:i,onRoleSelect:m,userInfo:l,isLoading:_})=>{const[u,a]=p.useState("buyer"),g=x=>{a(x)},h=()=>{m(u)};return n?e.jsx("div",{className:"role-modal-overlay",children:e.jsxs("div",{className:"role-modal",children:[e.jsxs("div",{className:"role-modal__header",children:[e.jsx("h2",{className:"role-modal__title",children:"Complete Your Registration"}),e.jsx("button",{className:"role-modal__close",onClick:i,disabled:_,children:e.jsx($,{})})]}),e.jsxs("div",{className:"role-modal__content",children:[e.jsxs("div",{className:"role-modal__user-info",children:[(l==null?void 0:l.photoURL)&&e.jsx("img",{src:l.photoURL,alt:"Profile",className:"role-modal__avatar"}),e.jsxs("div",{className:"role-modal__user-details",children:[e.jsx("h3",{className:"role-modal__user-name",children:l==null?void 0:l.displayName}),e.jsx("p",{className:"role-modal__user-email",children:l==null?void 0:l.email})]})]}),e.jsxs("div",{className:"role-modal__role-selection",children:[e.jsx("p",{className:"role-modal__label",children:"Select Your Account Type"}),e.jsxs("div",{className:"role-modal__options",children:[e.jsxs("div",{className:`role-modal__option ${u==="buyer"?"role-modal__option--selected":""}`,onClick:()=>g("buyer"),children:[e.jsx("div",{className:"role-modal__option-icon",children:e.jsx(D,{})}),e.jsxs("div",{className:"role-modal__option-content",children:[e.jsx("p",{className:"role-modal__option-title",children:"I want to learn"}),e.jsx("p",{className:"role-modal__option-description",children:"Browse and purchase training content from expert coaches"})]}),u==="buyer"&&e.jsx("div",{className:"role-modal__check-circle",children:e.jsx(y,{className:"role-modal__check-icon"})})]}),e.jsxs("div",{className:`role-modal__option ${u==="seller"?"role-modal__option--selected":""}`,onClick:()=>g("seller"),children:[e.jsx("div",{className:"role-modal__option-icon",children:e.jsx(M,{})}),e.jsxs("div",{className:"role-modal__option-content",children:[e.jsx("p",{className:"role-modal__option-title",children:"I want to teach"}),e.jsx("p",{className:"role-modal__option-description",children:"Create and sell your training content to athletes"})]}),u==="seller"&&e.jsx("div",{className:"role-modal__check-circle",children:e.jsx(y,{className:"role-modal__check-icon"})})]})]})]}),e.jsxs("div",{className:"role-modal__actions",children:[e.jsx("button",{className:"role-modal__button role-modal__button--secondary",onClick:i,disabled:_,children:"Cancel"}),e.jsx("button",{className:"role-modal__button role-modal__button--primary",onClick:h,disabled:_,children:_?"Creating Account...":"Complete Registration"})]})]})]})}):null},K=()=>{const n=O(),i=U(),{isLoading:m,isError:l,isSuccess:_,error:u}=L(o=>o.auth),[a,g]=p.useState({phone:"",countryCode:"+91"}),[h,x]=p.useState({}),[f,j]=p.useState(!1),[C,v]=p.useState(null),[k,N]=p.useState(null),w=o=>{const{name:s,value:c,type:t,checked:r}=o.target;g({...a,[s]:t==="checkbox"?r:c}),h[s]&&x({...h,[s]:null})},R=o=>{g({...a,countryCode:o.target.value})},F=()=>{const o={};return a.phone.trim()?/^\d{10}$/.test(a.phone)||(o.phone="Phone number must be 10 digits"):o.phone="Phone number is required",o},P=async o=>{var c;o.preventDefault();const s=F();if(Object.keys(s).length>0){x(s);return}i(b());try{const t=`${a.countryCode}${a.phone}`,r=await i(B({mobile:t})).unwrap();d.otp.success("OTP sent successfully!"),n("/otp-verification",{state:{userId:r.userId,phoneNumber:`${a.countryCode} ${a.phone}`,cooldownSeconds:r.cooldownSeconds||60,isLogin:!0,developmentOtp:r.developmentOtp}})}catch(t){if(t.includes("wait")&&t.includes("seconds")){const r=((c=t.match(/\d+/))==null?void 0:c[0])||60;d.otp.cooldown(parseInt(r))}else d.api.error({response:{data:{message:t}}})}},T=async()=>{try{if(i(b()),!S.isInitialized()){d.error("Firebase is not initialized. Please check your configuration.");return}const o=await S.signInWithGoogle();try{const s=await i(q(o.idToken)).unwrap();d.auth.loginSuccess(),s.user.role==="buyer"?n("/buyer/dashboard"):s.user.role==="seller"?n("/seller/dashboard"):s.user.role==="admin"?n("/admin/dashboard"):n("/")}catch(s){const c=typeof s=="string"?s:(s==null?void 0:s.message)||"";if(c.includes("not found")||c.includes("does not exist"))v(o.user),N(o.idToken),j(!0);else throw s}}catch(o){console.error("Google sign-in error:",o);const s=typeof o=="string"?o:(o==null?void 0:o.message)||"Failed to sign in with Google. Please try again.";d.error(s)}},G=async o=>{try{const{googleSignUp:s}=await z(async()=>{const{googleSignUp:t}=await import("./index-HMJiLbu5.js").then(r=>r.aC);return{googleSignUp:t}},__vite__mapDeps([0,1])),c=await i(s({idToken:k,role:o})).unwrap();d.auth.registrationSuccess(),j(!1),n(o==="buyer"?"/buyer/dashboard":o==="seller"?"/seller/dashboard":"/")}catch(s){console.error("Google sign-up error:",s),d.error(typeof s=="string"?s:"Failed to complete registration. Please try again.")}},E=()=>{j(!1),v(null),N(null)};return e.jsxs("div",{className:"auth-page auth-container",children:[e.jsxs("div",{className:"auth-form-container",children:[e.jsx("h1",{className:"auth-title",children:"Login in to your account"}),e.jsxs("form",{onSubmit:P,className:"auth-form",children:[e.jsxs("div",{className:"auth-form-input form-input-container",children:[e.jsxs("div",{className:"phone-input-wrapper",children:[e.jsx("div",{children:e.jsxs("div",{className:"country-code-select",children:[e.jsx(V,{style:{color:"var(--dark-gray)"}}),e.jsxs("select",{value:a.countryCode,onChange:R,className:"selectstylesnone",children:[e.jsx("option",{value:"+91",children:"+91"}),e.jsx("option",{value:"+1",children:"+1"}),e.jsx("option",{value:"+44",children:"+44"}),e.jsx("option",{value:"+61",children:"+61"}),e.jsx("option",{value:"+86",children:"+86"}),e.jsx("option",{value:"+49",children:"+49"}),e.jsx("option",{value:"+33",children:"+33"}),e.jsx("option",{value:"+81",children:"+81"}),e.jsx("option",{value:"+7",children:"+7"}),e.jsx("option",{value:"+55",children:"+55"})]})]})}),e.jsx("input",{type:"tel",id:"phone",name:"phone",value:a.phone,onChange:o=>{const s=o.target.value.replace(/\D/g,"");w({target:{name:"phone",value:s}})},placeholder:"Enter Phone Number",className:`form-input phone-input ${h.phone?"input-error":""}`,required:!0,pattern:"[0-9]*"})]}),h.phone&&e.jsx("p",{className:"error-message",children:h.phone})]}),e.jsx("button",{type:"submit",className:"signin-button",disabled:m,children:m?"Sending OTP...":"Sign In"}),e.jsx("div",{className:"auth-divider",children:e.jsx("span",{children:"or"})}),e.jsx(Y,{onClick:T,isLoading:m,text:"Sign in with Google",variant:"secondary"}),e.jsxs("p",{className:"signup-link mt-10",children:["Don't have an account? ",e.jsx(A,{to:"/signup",children:"Sign Up"})]})]})]}),e.jsx(I,{isOpen:f,onClose:E,onRoleSelect:G,userInfo:C,isLoading:m})]})};export{K as default};
