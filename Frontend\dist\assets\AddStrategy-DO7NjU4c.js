import{G as a,u as i,j as e}from"./index-HMJiLbu5.js";import{S as c}from"./SellerLayout-BF_wrScs.js";function o(t){return a({attr:{viewBox:"0 0 1024 1024"},child:[{tag:"path",attr:{d:"M872 474H286.9l350.2-304c5.6-4.9 2.2-14-5.2-14h-88.5c-3.9 0-7.6 1.4-10.5 3.9L155 487.8a31.96 31.96 0 0 0 0 48.3L535.1 866c1.5 1.3 3.3 2 5.2 2h91.5c7.4 0 10.8-9.2 5.2-14L286.9 550H872c4.4 0 8-3.6 8-8v-60c0-4.4-3.6-8-8-8z"},child:[]}]})(t)}function n(t){return a({attr:{viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},child:[{tag:"path",attr:{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"},child:[]},{tag:"polyline",attr:{points:"17 8 12 3 7 8"},child:[]},{tag:"line",attr:{x1:"12",y1:"3",x2:"12",y2:"15"},child:[]}]})(t)}const g=()=>{const t=i(),s="Enter a detailed description of your strategy here. Explain the key concepts, methodologies, and expected outcomes that buyers can expect from this strategy.",l="Share your background, experience, and expertise. Tell potential buyers about your coaching philosophy, achievements, and what makes your approach unique.",d="Outline what strategic content is included in this package. Detail the specific materials, resources, insights, and actionable steps provided to buyers.",r=()=>{t("/seller/my-sports-strategies")};return e.jsx(c,{children:e.jsxs("div",{className:"AddStrategy",children:[e.jsxs("div",{className:"AddStrategy__header",children:[e.jsxs("button",{className:"AddStrategy__back-btn",onClick:r,children:[e.jsx(o,{className:"AddStrategy__back-icon"}),"Back"]}),e.jsx("h1",{className:"AddStrategy__title",children:"Add New Strategy"})]}),e.jsxs("div",{className:"AddStrategy__form",children:[e.jsxs("div",{className:"AddStrategy__field",children:[e.jsx("label",{className:"AddStrategy__label",children:"Strategy Title"}),e.jsx("input",{type:"text",className:"AddStrategy__input",placeholder:"Add title for New Strategy",defaultValue:"Advanced Basketball Shooting Techniques"})]}),e.jsxs("div",{className:"AddStrategy__field",children:[e.jsx("label",{className:"AddStrategy__label",children:"Video Category"}),e.jsxs("select",{className:"AddStrategy__select",defaultValue:"basketball",children:[e.jsx("option",{value:"",children:"Select Category"}),e.jsx("option",{value:"basketball",children:"Basketball"}),e.jsx("option",{value:"football",children:"Football"}),e.jsx("option",{value:"soccer",children:"Soccer"}),e.jsx("option",{value:"baseball",children:"Baseball"}),e.jsx("option",{value:"tennis",children:"Tennis"}),e.jsx("option",{value:"golf",children:"Golf"}),e.jsx("option",{value:"other",children:"Other"})]})]}),e.jsxs("div",{className:"AddStrategy__field",children:[e.jsx("label",{className:"AddStrategy__label",children:"Description for Strategy"}),e.jsx("textarea",{className:"AddStrategy__textarea",placeholder:"Enter strategy description...",defaultValue:s,rows:6})]}),e.jsxs("div",{className:"AddStrategy__field",children:[e.jsx("label",{className:"AddStrategy__label",children:"About The Coach"}),e.jsx("textarea",{className:"AddStrategy__textarea",placeholder:"Tell us about yourself...",defaultValue:l,rows:6})]}),e.jsxs("div",{className:"AddStrategy__field",children:[e.jsx("label",{className:"AddStrategy__label",children:"Includes Strategic Content"}),e.jsx("textarea",{className:"AddStrategy__textarea",placeholder:"Describe what's included in this strategy...",defaultValue:d,rows:6})]}),e.jsxs("div",{className:"AddStrategy__field",children:[e.jsx("label",{className:"AddStrategy__label",children:"Upload Strategy Video"}),e.jsx("div",{className:"AddStrategy__upload",children:e.jsxs("div",{className:"AddStrategy__upload-content",children:[e.jsx(n,{className:"AddStrategy__upload-icon"}),e.jsx("p",{className:"AddStrategy__upload-text",children:"Drag & drop your video file here or click to browse"}),e.jsx("p",{className:"AddStrategy__upload-subtext",children:"Supported formats: MP4, MOV, AVI (Max size: 500MB)"})]})})]}),e.jsxs("div",{className:"AddStrategy__actions",children:[e.jsx("button",{className:"btn btn-primary AddStrategy__submit-btn",children:"Add New Strategy"}),e.jsx("button",{className:"btn btn-outline AddStrategy__reset-btn",children:"Reset Form"})]})]})]})})};export{g as default};
