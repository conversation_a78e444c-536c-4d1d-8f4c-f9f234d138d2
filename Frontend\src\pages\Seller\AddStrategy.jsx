import React from "react";
import { useNavigate } from "react-router-dom";
import SellerLayout from "../../components/seller/SellerLayout";
import { AiOutlineArrowLeft } from "react-icons/ai";
import { FiUpload } from "react-icons/fi";
import "../../styles/AddStrategy.css";

const AddStrategy = () => {
  const navigate = useNavigate();

  // Static placeholder content for text areas
  // TODO: Replace textareas with React Quill rich text editors
  // 1. Install: npm install react-quill --legacy-peer-deps
  // 2. Import: import ReactQuill from "react-quill"; import "react-quill/dist/quill.snow.css";
  // 3. Replace textarea elements with ReactQuill components
  const descriptionContent = "Enter a detailed description of your strategy here. Explain the key concepts, methodologies, and expected outcomes that buyers can expect from this strategy.";
  const aboutCoachContent = "Share your background, experience, and expertise. Tell potential buyers about your coaching philosophy, achievements, and what makes your approach unique.";
  const strategicContent = "Outline what strategic content is included in this package. Detail the specific materials, resources, insights, and actionable steps provided to buyers.";

  // Handle back button click
  const handleBackClick = () => {
    navigate("/seller/my-sports-strategies");
  };

  return (
    <SellerLayout>
      <div className="AddStrategy">
        {/* Header with Back Button */}
        <div className="AddStrategy__header">
          <button className="AddStrategy__back-btn" onClick={handleBackClick}>
            <AiOutlineArrowLeft className="AddStrategy__back-icon" />
            Back
          </button>
          <h1 className="AddStrategy__title">Add New Strategy</h1>
        </div>

        {/* Main Form */}
        <div className="AddStrategy__form">
          {/* Title Input */}
          <div className="AddStrategy__field">
            <label className="AddStrategy__label">Strategy Title</label>
            <input
              type="text"
              className="AddStrategy__input"
              placeholder="Add title for New Strategy"
              defaultValue="Advanced Basketball Shooting Techniques"
            />
          </div>

          {/* Video Category Dropdown */}
          <div className="AddStrategy__field">
            <label className="AddStrategy__label">Video Category</label>
            <select className="AddStrategy__select" defaultValue="basketball">
              <option value="">Select Category</option>
              <option value="basketball">Basketball</option>
              <option value="football">Football</option>
              <option value="soccer">Soccer</option>
              <option value="baseball">Baseball</option>
              <option value="tennis">Tennis</option>
              <option value="golf">Golf</option>
              <option value="other">Other</option>
            </select>
          </div>

          {/* Description Text Area */}
          <div className="AddStrategy__field">
            <label className="AddStrategy__label">Description for Strategy</label>
            <textarea
              className="AddStrategy__textarea"
              placeholder="Enter strategy description..."
              defaultValue={descriptionContent}
              rows={6}
            />
          </div>

          {/* About the Coach Text Area */}
          <div className="AddStrategy__field">
            <label className="AddStrategy__label">About The Coach</label>
            <textarea
              className="AddStrategy__textarea"
              placeholder="Tell us about yourself..."
              defaultValue={aboutCoachContent}
              rows={6}
            />
          </div>

          {/* Includes Strategic Content Text Area */}
          <div className="AddStrategy__field">
            <label className="AddStrategy__label">Includes Strategic Content</label>
            <textarea
              className="AddStrategy__textarea"
              placeholder="Describe what's included in this strategy..."
              defaultValue={strategicContent}
              rows={6}
            />
          </div>

          {/* Upload Section */}
          <div className="AddStrategy__field">
            <label className="AddStrategy__label">Upload Strategy Video</label>
            <div className="AddStrategy__upload">
              <div className="AddStrategy__upload-content">
                <FiUpload className="AddStrategy__upload-icon" />
                <p className="AddStrategy__upload-text">
                  Drag & drop your video file here or click to browse
                </p>
                <p className="AddStrategy__upload-subtext">
                  Supported formats: MP4, MOV, AVI (Max size: 500MB)
                </p>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="AddStrategy__actions">
            <button className="btn btn-primary AddStrategy__submit-btn">
              Add New Strategy
            </button>
            <button className="btn btn-outline AddStrategy__reset-btn">
              Reset Form
            </button>
          </div>
        </div>
      </div>
    </SellerLayout>
  );
};

export default AddStrategy;
