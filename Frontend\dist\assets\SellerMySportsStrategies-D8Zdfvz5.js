import{r,j as t,an as d}from"./index-HMJiLbu5.js";import{S as c}from"./SellerLayout-BF_wrScs.js";const o=[{id:1,title:"<PERSON> and Coaching Philosophies...",date:"20 May 2025 | 4:50PM",price:"$22.00",status:!0,thumbnail:"video-1.jpg"},{id:2,title:"<PERSON> - Early Transition Offensive Concepts",date:"20 May 2025 | 4:50PM",price:"$22.00",status:!0,thumbnail:"video-2.jpg"},{id:3,title:`WR Fundamentals
PoA - <PERSON>`,date:"20 May 2025 | 4:50PM",price:"$22.00",status:!1,thumbnail:"video-3.jpg"},{id:4,title:"<PERSON> and Coaching Philosophies...",date:"20 May 2025 | 4:50PM",price:"$22.00",status:!0,thumbnail:"video-1.jpg"},{id:5,title:"<PERSON> - Early Transition Offensive Concepts",date:"20 May 2025 | 4:50PM",price:"$22.00",status:!1,thumbnail:"video-2.jpg"},{id:6,title:`WR Fundamentals
PoA - <PERSON> Wiggins`,date:"20 May 2025 | 4:50PM",price:"$22.00",status:!1,thumbnail:"video-3.jpg"},{id:7,title:"Frank Martin - Drills and Coaching Philosophi<PERSON>...",date:"20 May 2025 | 4:50PM",price:"$22.00",status:!0,thumbnail:"video-1.jpg"},{id:8,title:"John Calipari - Early Transition Offensive Concepts",date:"20 May 2025 | 4:50PM",price:"$22.00",status:!1,thumbnail:"video-2.jpg"}],u=()=>{const[a,n]=r.useState(o),l=s=>{n(e=>e.map(i=>i.id===s?{...i,status:!i.status}:i))};return t.jsx(c,{children:t.jsx("div",{className:"video-status-container",children:t.jsxs("table",{className:"video-table",children:[t.jsx("thead",{children:t.jsxs("tr",{children:[t.jsx("th",{children:"No."}),t.jsx("th",{children:"Videos/Documents"}),t.jsx("th",{children:"Date"}),t.jsx("th",{children:"Price"}),t.jsx("th",{children:"Status"}),t.jsx("th",{children:"Action"})]})}),t.jsx("tbody",{children:a.map((s,e)=>t.jsxs("tr",{children:[t.jsx("td",{children:e+1}),t.jsx("td",{children:t.jsxs("div",{className:"video-doc",children:[t.jsx("img",{src:s.thumbnail,alt:"video thumb"}),t.jsx("span",{children:s.title})]})}),t.jsx("td",{children:s.date}),t.jsx("td",{children:s.price}),t.jsx("td",{children:t.jsxs("label",{className:"switch",children:[t.jsx("input",{type:"checkbox",checked:s.status,onChange:()=>l(s.id)}),t.jsx("span",{className:"slider round"})]})}),t.jsx("td",{children:t.jsx(d,{className:"action-icon"})})]},s.id))})]})})})};export{u as default};
