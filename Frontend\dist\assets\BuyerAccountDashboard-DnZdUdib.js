import{j as s,e as i,a1 as r,a4 as o,a6 as m,L as d,ai as n,Q as h}from"./index-HMJiLbu5.js";const x=({children:c,title:a,icon:t,className:e=""})=>s.jsxs("div",{className:`SectionWrapper ${e}`,children:[a&&s.jsx("div",{className:"bordrdiv mb-30",children:s.jsxs("h2",{className:"SectionWrapper__title",children:[t,a]})}),s.jsx("div",{className:"SectionWrapper__content",children:c})]}),j=()=>{const c=i(r),a=i(o),t=i(m);return s.jsx("div",{className:"BuyerAccountDashboard",children:s.jsxs(x,{icon:s.jsx(h,{className:"BuyerSidebar__icon"}),title:"Dashboard",children:[s.jsxs("div",{className:"stats",children:[s.jsxs("div",{className:"stat-card downloads",children:[s.jsx("div",{className:"stat-number",children:c.length.toString().padStart(2,"0")}),s.jsx("div",{className:"stat-label",children:"Total Downloads"})]}),s.jsxs("div",{className:"stat-card requests",children:[s.jsx("div",{className:"stat-number",children:a.length.toString().padStart(2,"0")}),s.jsx("div",{className:"stat-label",children:"Total Requests"})]}),s.jsxs("div",{className:"stat-card bids",children:[s.jsx("div",{className:"stat-number",children:t.length.toString().padStart(2,"0")}),s.jsx("div",{className:"stat-label",children:"Total Bids"})]})]}),s.jsxs("div",{className:"section",children:[s.jsxs("div",{className:"section-header",children:[s.jsx("h3",{className:"section-title",children:"My Downloads"}),s.jsx(d,{to:"/buyer/account/downloads",className:"view-all",children:"View All Downloads"})]}),s.jsxs("div",{className:"table",children:[s.jsxs("div",{className:"table-header",children:[s.jsx("div",{className:"table-cell no",children:"No."}),s.jsx("div",{className:"table-cell order-id",children:"Order Id"}),s.jsx("div",{className:"table-cell video",children:"Videos/Documents"}),s.jsx("div",{className:"table-cell date",children:"Date"}),s.jsx("div",{className:"table-cell amount",children:"Amount"}),s.jsx("div",{className:"table-cell status",children:"Status"}),s.jsx("div",{className:"table-cell action",children:"Action"})]}),c.slice(0,2).map((e,l)=>s.jsxs("div",{className:"table-row",children:[s.jsx("div",{className:"table-cell no",children:l+1}),s.jsxs("div",{className:"table-cell order-id",children:["#",e.id]}),s.jsx("div",{className:"table-cell video",children:s.jsxs("div",{className:"content-item",children:[s.jsx("div",{className:"content-image",children:s.jsx("img",{src:"https://images.unsplash.com/photo-1566577739112-5180d4bf9390?q=80&w=300&h=200&auto=format&fit=crop",alt:e.title})}),s.jsxs("div",{className:"content-info",children:[s.jsx("div",{className:"content-title",children:e.title}),s.jsxs("div",{className:"content-coach",children:["By ",e.coach]})]})]})}),s.jsxs("div",{className:"table-cell date",children:[e.downloadDate," | 4:30PM"]}),s.jsxs("div",{className:"table-cell amount",children:["$",(Math.random()*30+20).toFixed(2)]}),s.jsx("div",{className:"table-cell status",children:s.jsx("span",{className:"status-badge downloaded",children:"Downloaded"})}),s.jsx("div",{className:"table-cell action",children:s.jsx("button",{className:"action-btn",children:s.jsx(n,{})})})]},e.id))]})]}),s.jsxs("div",{className:"section",children:[s.jsxs("div",{className:"section-header",children:[s.jsx("h3",{className:"section-title",children:"My Requests"}),s.jsx(d,{to:"/buyer/account/requests",className:"view-all",children:"View All Requests"})]}),s.jsxs("div",{className:"table",children:[s.jsxs("div",{className:"table-header",children:[s.jsx("div",{className:"table-cell no",children:"No."}),s.jsx("div",{className:"table-cell order-id",children:"Order Id"}),s.jsx("div",{className:"table-cell video",children:"Videos/Documents"}),s.jsx("div",{className:"table-cell date",children:"Date"}),s.jsx("div",{className:"table-cell amount",children:"Requested Amount"}),s.jsx("div",{className:"table-cell status",children:"Status"}),s.jsx("div",{className:"table-cell action",children:"Action"})]}),a.slice(0,2).map((e,l)=>s.jsxs("div",{className:"table-row",children:[s.jsx("div",{className:"table-cell no",children:l+1}),s.jsxs("div",{className:"table-cell order-id",children:["#",e.id]}),s.jsx("div",{className:"table-cell video",children:s.jsxs("div",{className:"content-item",children:[s.jsx("div",{className:"content-image",children:s.jsx("img",{src:"https://images.unsplash.com/photo-1566577739112-5180d4bf9390?q=80&w=300&h=200&auto=format&fit=crop",alt:e.title})}),s.jsxs("div",{className:"content-info",children:[s.jsx("div",{className:"content-title",children:e.title}),s.jsx("div",{className:"content-coach",children:"By Coach"})]})]})}),s.jsxs("div",{className:"table-cell date",children:[e.date," | 4:30PM"]}),s.jsxs("div",{className:"table-cell amount",children:["$",(Math.random()*30+20).toFixed(2)]}),s.jsx("div",{className:"table-cell status",children:s.jsx("span",{className:`status-badge ${e.status}`,children:e.status})}),s.jsx("div",{className:"table-cell action",children:s.jsx("button",{className:"action-btn",children:s.jsx(n,{})})})]},e.id))]})]}),s.jsxs("div",{className:"section",children:[s.jsxs("div",{className:"section-header",children:[s.jsx("h3",{className:"section-title",children:"My Bids"}),s.jsx(d,{to:"/buyer/account/bids",className:"view-all",children:"View All Bids"})]}),s.jsxs("div",{className:"table",children:[s.jsxs("div",{className:"table-header",children:[s.jsx("div",{className:"table-cell no",children:"No."}),s.jsx("div",{className:"table-cell order-id",children:"Bid Id"}),s.jsx("div",{className:"table-cell video",children:"Videos/Documents"}),s.jsx("div",{className:"table-cell date",children:"Date"}),s.jsx("div",{className:"table-cell amount",children:"Bid Amount"}),s.jsx("div",{className:"table-cell status",children:"Status"}),s.jsx("div",{className:"table-cell action",children:"Action"})]}),t.slice(0,2).map((e,l)=>s.jsxs("div",{className:"table-row",children:[s.jsx("div",{className:"table-cell no",children:l+1}),s.jsxs("div",{className:"table-cell order-id",children:["#",e.id]}),s.jsx("div",{className:"table-cell video",children:s.jsxs("div",{className:"content-item",children:[s.jsx("div",{className:"content-image",children:s.jsx("img",{src:"https://images.unsplash.com/photo-1566577739112-5180d4bf9390?q=80&w=300&h=200&auto=format&fit=crop",alt:e.title})}),s.jsxs("div",{className:"content-info",children:[s.jsx("div",{className:"content-title",children:e.title}),s.jsxs("div",{className:"content-coach",children:["By ",e.coach]})]})]})}),s.jsxs("div",{className:"table-cell date",children:[e.date," | 4:30PM"]}),s.jsxs("div",{className:"table-cell amount",children:["$",e.bidAmount.toFixed(2)]}),s.jsx("div",{className:"table-cell status",children:s.jsx("span",{className:`status-badge ${e.status}`,children:e.status})}),s.jsx("div",{className:"table-cell action",children:s.jsx("button",{className:"action-btn",children:s.jsx(n,{})})})]},e.id))]})]})]})})},v=Object.freeze(Object.defineProperty({__proto__:null,default:j},Symbol.toStringTag,{value:"Module"}));export{j as B,x as S,v as a};
