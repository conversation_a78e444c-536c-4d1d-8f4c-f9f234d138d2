{"name": "react-quill", "version": "1.3.5", "description": "The Quill rich-text editor as a React component.", "author": "zenoamaro <<EMAIL>>", "homepage": "https://github.com/zenoamaro/react-quill", "license": "MIT", "bugs": {"url": "https://github.com/zenoamaro/react-quill/issues", "email": "<EMAIL>"}, "repository": {"type": "git", "url": "https://github.com/zenoamaro/react-quill.git"}, "main": "lib/index.js", "browser": "lib/index.js", "types": "types.d.ts", "scripts": {"test": "make test", "build": "make build", "prepack": "make build", "postpack": "make clean", "prepublish": "make build", "postpublish": "make clean"}, "engines": {"node": ">= 0.8.x"}, "keywords": ["react", "react-component", "rich", "text", "rich-text", "textarea", "quill"], "files": ["dist/", "lib/", "types.d.ts", "README.md", "CHANGELOG.md", "LICENSE"], "dependencies": {"@types/quill": "1.3.10", "create-react-class": "^15.6.0", "lodash": "^4.17.4", "prop-types": "^15.5.10", "quill": "^1.3.7", "react-dom-factories": "^1.0.0"}, "peerDependencies": {"react": "^0.14.9 || ^15.3.0 || ^16.0.0"}, "devDependencies": {"@types/react": "^15.3.0", "blanket": "^1.2.3", "chai": "^4.1.0", "chai-enzyme": "^0.8.0", "cheerio": "^1.0.0-rc.1", "enzyme": "^2.9.1", "jsdom": "^11.0.0", "jsdom-global": "^3.0.2", "jshint": "^2.9.4", "mocha": "^3.4.2", "mocha-text-cov": "^0.1.1", "react": "^15.6.1", "react-addons-test-utils": "^15.6.0", "react-dom": "^15.6.1", "react-test-renderer": "^15.6.1", "should": "^4.3.0", "sinon": "^2.3.5", "travis-cov": "^0.2.5", "uglify-js": "^3.0.18", "webpack": "^1.4.13"}}